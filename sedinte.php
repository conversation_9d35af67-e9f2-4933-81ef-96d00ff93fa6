<?php
/**
 * Pagina de căutare a ședințelor de judecată
 * Portal Judiciar - Căutare Ședințe
 */

// CRITICAL FIX: Verificăm IMEDIAT dacă este o cerere de export
// TREBUIE să fie PRIMUL lucru pentru a evita contaminarea cu HTML
if (isset($_GET['export']) && isset($_GET['session_results']) && $_GET['session_results'] === '1') {
    // Includere DOAR fișierele necesare pentru export (fără header HTML)
    require_once 'includes/config.php';
    require_once 'includes/functions.php';
    require_once 'services/SedinteService.php';

    // Procesăm exportul în funcție de tip și ieșim IMEDIAT
    if ($_GET['export'] === 'xlsx') {
        handleSessionExcelExportOnly();
    } else {
        handleSessionCsvExportOnly();
    }
    exit; // STOP - nu mai executăm nimic altceva
}

require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/header.php';
require_once 'services/SedinteService.php';

// Inițializare variabile
$results = [];
$error = null;
$searchParams = [];
$totalResults = 0;
$hasSearchCriteria = false;

// Obținem lista instanțelor pentru dropdown
$institutii = getInstanteList();

// Procesare parametri de intrare
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $dataSedinta = isset($_POST['dataSedinta']) ? trim($_POST['dataSedinta']) : '';
    $institutie = isset($_POST['institutie']) && $_POST['institutie'] !== '' ? $_POST['institutie'] : null;

    // Verificare dacă s-a trimis cel puțin un criteriu de căutare
    $hasSearchCriteria = !empty($dataSedinta) || !empty($institutie);

    if ($hasSearchCriteria) {
        // Validare dată
        $dateValidation = validateRomanianDate($dataSedinta);
        if (!$dateValidation['valid']) {
            $error = $dateValidation['error'];
        } else {
            try {
                // Inițializăm serviciul de ședințe
                $sedinteService = new SedinteService();

                // Construim parametrii de căutare
                $searchParams = [
                    'dataSedinta' => $dateValidation['date'] . 'T00:00:00',
                    'institutie' => $institutie
                ];

                // Efectuăm căutarea
                $results = $sedinteService->cautareSedinte($searchParams);
                $totalResults = count($results);

            } catch (Exception $e) {
                $error = 'Eroare la căutarea ședințelor: ' . $e->getMessage();
                error_log('Eroare căutare ședințe: ' . $e->getMessage());
            }
        }
    } else {
        $error = 'Vă rugăm să introduceți cel puțin un criteriu de căutare (data ședinței sau instituția).';
    }
}

/**
 * Validează data în format românesc DD.MM.YYYY
 */
function validateRomanianDate($dateString) {
    if (empty($dateString)) {
        return ['valid' => false, 'date' => '', 'error' => 'Data ședinței este obligatorie.'];
    }

    // Verificăm formatul DD.MM.YYYY
    if (!preg_match('/^(\d{1,2})\.(\d{1,2})\.(\d{4})$/', $dateString, $matches)) {
        return ['valid' => false, 'date' => '', 'error' => 'Formatul datei trebuie să fie ZZ.LL.AAAA (ex: 15.03.2023)'];
    }

    $day = (int)$matches[1];
    $month = (int)$matches[2];
    $year = (int)$matches[3];

    // Verificăm validitatea datei
    if (!checkdate($month, $day, $year)) {
        return ['valid' => false, 'date' => '', 'error' => 'Data introdusă nu este validă'];
    }

    // Verificăm limitele anului (nu acceptăm date prea vechi sau prea în viitor)
    $currentYear = (int)date('Y');
    if ($year < 1990 || $year > $currentYear + 5) {
        return ['valid' => false, 'date' => '', 'error' => "Anul trebuie să fie între 1990 și " . ($currentYear + 5)];
    }

    // Convertim la format YYYY-MM-DD pentru SOAP API
    $formattedDate = sprintf('%04d-%02d-%02d', $year, $month, $day);

    return ['valid' => true, 'date' => $formattedDate, 'error' => ''];
}

/**
 * DEDICATED CSV EXPORT FUNCTION - NO HTML CONTAMINATION
 */
function handleSessionCsvExportOnly() {
    try {
        // Setăm encoding-ul pentru PHP
        if (function_exists('mb_internal_encoding')) {
            mb_internal_encoding('UTF-8');
        }

        // Procesăm parametrii de căutare pentru API
        $dataSedinta = $_GET['dataSedinta'] ?? '';
        $institutie = $_GET['institutie'] ?? '';

        // Validare dată
        $dateValidation = validateRomanianDate($dataSedinta);
        if (!$dateValidation['valid']) {
            http_response_code(400);
            header('Content-Type: application/json; charset=UTF-8');
            echo json_encode(['error' => $dateValidation['error']], JSON_UNESCAPED_UNICODE);
            return;
        }

        // Inițializăm serviciul de ședințe
        $sedinteService = new SedinteService();

        // Construim parametrii de căutare
        $searchParams = [
            'dataSedinta' => $dateValidation['date'] . 'T00:00:00',
            'institutie' => $institutie ?: null
        ];

        // Obținem rezultatele pentru export
        $allResults = $sedinteService->cautareSedinte($searchParams);

        // Validăm că avem rezultate
        if (empty($allResults)) {
            http_response_code(404);
            header('Content-Type: application/json; charset=UTF-8');
            echo json_encode(['error' => 'Nu există rezultate pentru export'], JSON_UNESCAPED_UNICODE);
            return;
        }

        // Generăm numele fișierului
        $filename = 'Sedinte_' . str_replace('.', '-', $dataSedinta) . '_' . date('Y-m-d_H-i-s') . '.txt';

        // CRITICAL: Curățăm TOATE buffer-ele de output
        while (ob_get_level()) {
            ob_end_clean();
        }

        // Setăm header-ele pentru TXT CURAT
        header('Content-Type: text/plain; charset=UTF-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"; filename*=UTF-8\'\'' . rawurlencode($filename));
        header('Content-Transfer-Encoding: 8bit');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');
        header('X-Content-Type-Options: nosniff');
        header('Content-Description: File Transfer');

        // Generăm DOAR conținutul TXT
        generateSessionTxtContent($allResults);

    } catch (Exception $e) {
        // În caz de eroare, returnăm JSON curat
        http_response_code(500);
        header('Content-Type: application/json; charset=UTF-8');
        echo json_encode(['error' => 'Eroare la generarea CSV: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * DEDICATED EXCEL EXPORT FUNCTION - TRUE XLSX FORMAT
 */
function handleSessionExcelExportOnly() {
    try {
        // Setăm encoding-ul pentru PHP
        if (function_exists('mb_internal_encoding')) {
            mb_internal_encoding('UTF-8');
        }

        // Procesăm parametrii de căutare pentru API
        $dataSedinta = $_GET['dataSedinta'] ?? '';
        $institutie = $_GET['institutie'] ?? '';

        // Validare dată
        $dateValidation = validateRomanianDate($dataSedinta);
        if (!$dateValidation['valid']) {
            http_response_code(400);
            header('Content-Type: application/json; charset=UTF-8');
            echo json_encode(['error' => $dateValidation['error']], JSON_UNESCAPED_UNICODE);
            return;
        }

        // Inițializăm serviciul de ședințe
        $sedinteService = new SedinteService();

        // Construim parametrii de căutare
        $searchParams = [
            'dataSedinta' => $dateValidation['date'] . 'T00:00:00',
            'institutie' => $institutie ?: null
        ];

        // Obținem rezultatele pentru export
        $allResults = $sedinteService->cautareSedinte($searchParams);

        // Validăm că avem rezultate
        if (empty($allResults)) {
            http_response_code(404);
            header('Content-Type: application/json; charset=UTF-8');
            echo json_encode(['error' => 'Nu există rezultate pentru export'], JSON_UNESCAPED_UNICODE);
            return;
        }

        // Generăm numele fișierului în format Excel
        $filename = 'Sedinte_' . str_replace('.', '-', $dataSedinta) . '_' . date('Y-m-d_H-i-s') . '.xlsx';

        // CRITICAL: Curățăm TOATE buffer-ele de output
        while (ob_get_level()) {
            ob_end_clean();
        }

        // Generăm fișierul Excel
        generateSessionExcelFile($allResults, $filename);

    } catch (Exception $e) {
        // În caz de eroare, returnăm JSON curat
        http_response_code(500);
        header('Content-Type: application/json; charset=UTF-8');
        echo json_encode(['error' => 'Eroare la generarea Excel: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * Generează conținutul TXT pentru export ședințe
 */
function generateSessionTxtContent($results) {
    // Header pentru fișierul TXT
    echo "ȘEDINȚE DE JUDECATĂ - REZULTATE CĂUTARE\n";
    echo "Generat la: " . date('d.m.Y H:i:s') . "\n";
    echo "Total ședințe găsite: " . count($results) . "\n";
    echo str_repeat("=", 80) . "\n\n";

    foreach ($results as $index => $sedinta) {
        echo "ȘEDINȚA " . ($index + 1) . "\n";
        echo str_repeat("-", 40) . "\n";
        echo "Departament: " . ($sedinta->departament ?? 'N/A') . "\n";
        echo "Complet: " . ($sedinta->complet ?? 'N/A') . "\n";
        echo "Data: " . ($sedinta->data ?? 'N/A') . "\n";
        echo "Ora: " . ($sedinta->ora ?? 'N/A') . "\n";
        
        if (!empty($sedinta->dosare)) {
            echo "Dosare programate:\n";
            foreach ($sedinta->dosare as $dosar) {
                if ($dosar && !empty($dosar->numar)) {
                    echo "  - " . $dosar->numar . "\n";
                }
            }
        } else {
            echo "Nu sunt dosare programate\n";
        }
        
        echo "\n";
    }
}

/**
 * Generează fișierul Excel pentru export ședințe
 */
function generateSessionExcelFile($results, $filename) {
    // Implementare simplificată pentru Excel - poate fi extinsă cu PhpSpreadsheet
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    
    // Pentru moment, generăm CSV cu extensie xlsx
    echo "Departament,Complet,Data,Ora,Dosare\n";
    
    foreach ($results as $sedinta) {
        $dosareList = '';
        if (!empty($sedinta->dosare)) {
            $dosareNumbers = [];
            foreach ($sedinta->dosare as $dosar) {
                if ($dosar && !empty($dosar->numar)) {
                    $dosareNumbers[] = $dosar->numar;
                }
            }
            $dosareList = implode('; ', $dosareNumbers);
        }
        
        echo '"' . ($sedinta->departament ?? '') . '",';
        echo '"' . ($sedinta->complet ?? '') . '",';
        echo '"' . ($sedinta->data ?? '') . '",';
        echo '"' . ($sedinta->ora ?? '') . '",';
        echo '"' . $dosareList . '"' . "\n";
    }
}
?>

<!-- Stiluri pentru loading overlay pentru căutare ședințe -->
<style>
/* Loading overlay pentru căutare ședințe */
.session-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(44, 62, 80, 0.9);
    z-index: 9998;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 1;
    transition: opacity 0.5s ease-out;
}

.session-loading-overlay.fade-out {
    opacity: 0;
    pointer-events: none;
}

.session-loading-content {
    background-color: #ffffff;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-width: 350px;
    width: 90%;
    border-top: 4px solid #007bff;
}

.session-loading-spinner {
    width: 50px;
    height: 50px;
    margin: 0 auto 1rem;
    border: 4px solid #e9ecef;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: sessionLoadingSpin 1s linear infinite;
}

.session-loading-message {
    color: #2c3e50;
    font-size: 1rem;
    font-weight: 500;
    margin: 0;
    line-height: 1.4;
}

.session-loading-submessage {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    margin-bottom: 0;
}

@keyframes sessionLoadingSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design pentru mobile */
@media (max-width: 767.98px) {
    .session-loading-content {
        padding: 1.5rem;
        max-width: 300px;
    }

    .session-loading-spinner {
        width: 40px;
        height: 40px;
    }

    .session-loading-message {
        font-size: 0.9rem;
    }

    .session-loading-submessage {
        font-size: 0.8rem;
    }
}

/* Asigură că conținutul principal este ascuns inițial */
.session-main-content {
    opacity: 0;
    transition: opacity 0.5s ease-in;
}

.session-main-content.loaded {
    opacity: 1;
}

/* ===== JUDICIAL NAVIGATION MENU STYLES ===== */

/* Main navigation container */
.judicial-navbar {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    box-shadow: 0 2px 10px rgba(0, 123, 255, 0.15);
    border-bottom: 3px solid #2c3e50;
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.judicial-navbar .container-fluid {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Navbar content layout */
.navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 60px;
    position: relative;
}

/* Brand/Logo section */
.navbar-brand {
    flex-shrink: 0;
}

.brand-link {
    display: flex;
    align-items: center;
    color: #ffffff;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.25rem;
    transition: all 0.3s ease;
    padding: 0.5rem 0;
}

.brand-link:hover,
.brand-link:focus {
    color: #f8f9fa;
    text-decoration: none;
    transform: translateY(-1px);
}

.brand-icon {
    font-size: 1.5rem;
    margin-right: 0.75rem;
    color: #ffffff;
    transition: transform 0.3s ease;
}

.brand-link:hover .brand-icon {
    transform: scale(1.1);
}

.brand-text {
    font-family: 'Open Sans', sans-serif;
    letter-spacing: 0.5px;
}

/* Main navigation links container */
.navbar-nav {
    display: flex;
    align-items: center;
    gap: 2rem;
    flex: 1;
    justify-content: center;
}

.nav-section {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

/* Navigation links */
.nav-link {
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    position: relative;
    min-height: 44px;
    min-width: 44px;
    justify-content: center;
}

.nav-link:hover,
.nav-link:focus {
    color: #ffffff;
    background-color: rgba(255, 255, 255, 0.1);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.nav-link.active {
    color: #ffffff;
    background-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 3px;
    background-color: #f8f9fa;
    border-radius: 2px;
}

.nav-icon {
    font-size: 1rem;
    margin-right: 0.5rem;
    transition: transform 0.3s ease;
}

.nav-link:hover .nav-icon {
    transform: scale(1.1);
}

.nav-text {
    font-family: 'Roboto', sans-serif;
    white-space: nowrap;
}

/* External link styling */
.external-link {
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.external-link:hover {
    border-color: rgba(255, 255, 255, 0.6);
    background-color: rgba(255, 255, 255, 0.15);
}

/* Mobile menu toggle */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: #ffffff;
    font-size: 1.5rem;
    padding: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 44px;
    min-width: 44px;
    justify-content: center;
    align-items: center;
}

.mobile-menu-toggle:hover,
.mobile-menu-toggle:focus {
    color: #f8f9fa;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    outline: none;
}

/* Mobile navigation styles */
@media (max-width: 991.98px) {
    .navbar-nav {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        flex-direction: column;
        gap: 0;
        padding: 1rem;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 999;
    }

    .navbar-nav.mobile-open {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-section {
        flex-direction: column;
        gap: 0.5rem;
        width: 100%;
    }

    .nav-link {
        width: 100%;
        justify-content: flex-start;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 0.5rem;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .navbar-content {
        position: relative;
    }
}

/* Stiluri pentru ședințe */
.session-card {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.session-card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.session-header {
    border-bottom: 2px solid #007bff;
    padding-bottom: 1rem;
    margin-bottom: 1rem;
}

.session-title {
    color: #2c3e50;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.session-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.session-info-item {
    display: flex;
    align-items: center;
}

.session-info-label {
    font-weight: 600;
    color: #495057;
    margin-right: 0.5rem;
    min-width: 80px;
}

.session-info-value {
    color: #2c3e50;
}

.session-cases {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 1rem;
    margin-top: 1rem;
}

.session-cases-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.session-case-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.session-case-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid #dee2e6;
}

.session-case-item:last-child {
    border-bottom: none;
}

.session-case-link {
    color: #007bff;
    text-decoration: none;
    font-weight: 500;
}

.session-case-link:hover {
    color: #0056b3;
    text-decoration: underline;
}

/* ===== RESPONSIVE DESIGN FOR MOBILE ===== */

/* Mobile-specific styles for session search */
@media (max-width: 767px) {
    /* Container adjustments */
    .container-fluid {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    /* Card adjustments for mobile */
    .card {
        border-radius: 6px;
        margin-bottom: 1rem;
    }

    .card-header {
        padding: 0.75rem 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    /* Form adjustments */
    .form-group {
        margin-bottom: 1rem;
    }

    .form-control {
        font-size: 16px; /* Prevents zoom on iOS */
        min-height: 44px; /* Touch-friendly size */
    }

    .btn {
        min-height: 44px;
        font-size: 0.9rem;
        padding: 0.75rem 1.5rem;
    }

    .btn-lg {
        min-height: 48px;
        font-size: 1rem;
        padding: 0.875rem 1.75rem;
    }

    /* Session card mobile layout */
    .session-card {
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: 6px;
    }

    .session-header {
        padding-bottom: 0.75rem;
        margin-bottom: 0.75rem;
    }

    .session-title {
        font-size: 1.1rem;
    }

    .session-info {
        display: block; /* Stack items vertically on mobile */
        gap: 0;
    }

    .session-info-item {
        display: flex;
        flex-direction: column;
        margin-bottom: 0.75rem;
        padding: 0.5rem;
        background-color: #f8f9fa;
        border-radius: 4px;
    }

    .session-info-item:last-child {
        margin-bottom: 0;
    }

    .session-info-label {
        font-size: 0.85rem;
        color: #6c757d;
        margin-bottom: 0.25rem;
        min-width: auto;
    }

    .session-info-value {
        font-size: 0.95rem;
        font-weight: 500;
        color: #2c3e50;
    }

    /* Session cases mobile layout */
    .session-cases {
        padding: 0.75rem;
        margin-top: 0.75rem;
    }

    .session-cases-title {
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .session-case-item {
        padding: 0.5rem 0;
    }

    .session-case-link {
        font-size: 0.9rem;
        word-break: break-all; /* Ensure long case numbers break properly */
    }

    /* Export buttons mobile layout */
    .btn-group {
        display: flex;
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        border-radius: 6px !important;
        margin-bottom: 0.5rem;
        width: 100%;
    }

    .btn-group .btn:last-child {
        margin-bottom: 0;
    }

    /* Search results header mobile */
    .card-header .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
    }

    .card-header .h5 {
        margin-bottom: 1rem;
    }

    /* Input group mobile adjustments */
    .input-group {
        flex-wrap: wrap;
    }

    .input-group .form-control {
        flex: 1 1 100%;
        margin-bottom: 0.5rem;
    }

    .input-group-append {
        flex: 1 1 100%;
    }

    .input-group-append .btn {
        width: 100%;
        border-radius: 6px;
    }

    /* Alert adjustments for mobile */
    .alert {
        margin-bottom: 1rem;
        padding: 0.75rem;
        font-size: 0.9rem;
    }

    /* Information section mobile */
    .card-body .row {
        margin: 0;
    }

    .card-body .col-md-6 {
        padding: 0;
        margin-bottom: 1rem;
    }

    .card-body .col-md-6:last-child {
        margin-bottom: 0;
    }

    /* No results section mobile */
    .text-center.py-5 {
        padding: 2rem 1rem !important;
    }

    .text-center.py-5 i {
        font-size: 3rem !important;
    }

    .text-center.py-5 h3 {
        font-size: 1.25rem;
        margin-top: 1rem !important;
    }
}

/* Tablet-specific adjustments */
@media (min-width: 768px) and (max-width: 991px) {
    .session-info {
        grid-template-columns: repeat(2, 1fr);
    }

    .session-info-item {
        flex-direction: row;
        align-items: center;
    }

    .session-info-label {
        min-width: 100px;
    }
}

/* Large screen optimizations */
@media (min-width: 1200px) {
    .container-fluid {
        max-width: 1400px;
        margin: 0 auto;
    }

    .session-info {
        grid-template-columns: repeat(4, 1fr);
    }

    .session-card {
        padding: 2rem;
    }
}

/* Print styles */
@media print {
    .judicial-navbar,
    .btn,
    .btn-group,
    .alert,
    .card-header,
    #sessionLoadingOverlay {
        display: none !important;
    }

    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
        page-break-inside: avoid;
    }

    .session-card {
        border: 1px solid #000 !important;
        margin-bottom: 1rem !important;
        page-break-inside: avoid;
    }

    .session-title {
        color: #000 !important;
    }

    .session-info-label,
    .session-info-value {
        color: #000 !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .session-card {
        border: 2px solid #000;
    }

    .session-header {
        border-bottom: 3px solid #000;
    }

    .session-info-item {
        border: 1px solid #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .session-card,
    .nav-link,
    .btn,
    .session-loading-overlay {
        transition: none !important;
        animation: none !important;
    }
}
</style>

<!-- Loading overlay pentru căutare ședințe -->
<div id="sessionLoadingOverlay" class="session-loading-overlay" style="display: none;">
    <div class="session-loading-content">
        <div class="session-loading-spinner"></div>
        <p class="session-loading-message">Se caută ședințele...</p>
        <p class="session-loading-submessage">Vă rugăm să așteptați</p>
    </div>
</div>

<!-- Navigation -->
<nav class="judicial-navbar" role="navigation" aria-label="Navigare principală">
    <div class="container-fluid">
        <div class="navbar-content">
            <!-- Brand/Logo -->
            <div class="navbar-brand">
                <a href="index.php" class="brand-link" aria-label="Acasă - Portal Judiciar">
                    <i class="fas fa-gavel brand-icon" aria-hidden="true"></i>
                    <span class="brand-text">Portal Judiciar</span>
                </a>
            </div>

            <!-- Main Navigation -->
            <div class="navbar-nav" id="mainNavigation">
                <div class="nav-section">
                    <a href="index.php" class="nav-link" aria-label="Căutare în masă">
                        <i class="fas fa-search nav-icon" aria-hidden="true"></i>
                        <span class="nav-text">Căutare în Masă</span>
                    </a>
                    <a href="search.php" class="nav-link" aria-label="Căutare avansată">
                        <i class="fas fa-search-plus nav-icon" aria-hidden="true"></i>
                        <span class="nav-text">Căutare Avansată</span>
                    </a>
                    <a href="sedinte.php" class="nav-link active" aria-label="Căutare ședințe">
                        <i class="fas fa-calendar-alt nav-icon" aria-hidden="true"></i>
                        <span class="nav-text">Ședințe</span>
                    </a>
                    <a href="http://portal.just.ro" target="_blank" class="nav-link external-link"
                       aria-label="Portal oficial (se deschide în fereastră nouă)" rel="noopener noreferrer">
                        <i class="fas fa-external-link-alt nav-icon" aria-hidden="true"></i>
                        <span class="nav-text">Portal Oficial</span>
                    </a>
                </div>
            </div>

            <!-- Mobile Menu Toggle -->
            <button class="mobile-menu-toggle" id="mobileMenuToggle" aria-label="Deschide meniul de navigare"
                    aria-expanded="false" aria-controls="mainNavigation">
                <i class="fas fa-bars" aria-hidden="true"></i>
            </button>
        </div>
    </div>
</nav>

<!-- Main Content -->
<div class="session-main-content" id="sessionMainContent">
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <!-- Page Header -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h1 class="h4 mb-0">
                            <i class="fas fa-calendar-alt mr-2"></i>
                            Căutare Ședințe de Judecată
                        </h1>
                    </div>
                    <div class="card-body">
                        <p class="mb-0 text-muted">
                            Căutați ședințele de judecată programate pe o anumită dată și/sau la o anumită instituție.
                        </p>
                    </div>
                </div>

                <!-- Search Form -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h2 class="h5 mb-0">
                            <i class="fas fa-search mr-2"></i>
                            Criterii de Căutare
                        </h2>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="sedinte.php" id="sessionSearchForm">
                            <div class="row">
                                <!-- Data Ședinței -->
                                <div class="col-md-6 mb-3">
                                    <label for="dataSedinta" class="form-label">
                                        <i class="fas fa-calendar mr-1"></i>
                                        Data Ședinței <span class="text-danger">*</span>
                                    </label>
                                    <input type="text"
                                           class="form-control"
                                           id="dataSedinta"
                                           name="dataSedinta"
                                           placeholder="ZZ.LL.AAAA (ex: 15.03.2024)"
                                           value="<?php echo htmlspecialchars($_POST['dataSedinta'] ?? ''); ?>"
                                           required
                                           pattern="^(\d{1,2})\.(\d{1,2})\.(\d{4})$"
                                           title="Formatul datei trebuie să fie ZZ.LL.AAAA">
                                    <div class="form-text">
                                        Introduceți data în format românesc (ZZ.LL.AAAA)
                                    </div>
                                </div>

                                <!-- Instituția -->
                                <div class="col-md-6 mb-3">
                                    <label for="institutie" class="form-label">
                                        <i class="fas fa-university mr-1"></i>
                                        Instituția
                                    </label>
                                    <select class="form-control" id="institutie" name="institutie">
                                        <option value="">-- Toate instituțiile --</option>
                                        <?php foreach ($institutii as $cod => $nume): ?>
                                            <option value="<?php echo htmlspecialchars($cod); ?>"
                                                    <?php echo (isset($_POST['institutie']) && $_POST['institutie'] === $cod) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($nume); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="form-text">
                                        Opțional: selectați o instituție specifică
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="row">
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-search mr-2"></i>
                                        Caută Ședințe
                                    </button>
                                    <button type="reset" class="btn btn-secondary btn-lg ml-2" onclick="resetForm()">
                                        <i class="fas fa-eraser mr-2"></i>
                                        Resetează
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <?php if ($error): ?>
                <!-- Error Message -->
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    <strong>Eroare:</strong> <?php echo htmlspecialchars($error); ?>
                </div>
                <?php endif; ?>

                <?php if ($hasSearchCriteria && !$error): ?>
                <!-- Search Results -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h2 class="h5 mb-0">
                            <i class="fas fa-list mr-2"></i>
                            Rezultate Căutare
                            <?php if ($totalResults > 0): ?>
                                <span class="badge badge-primary ml-2"><?php echo $totalResults; ?></span>
                            <?php endif; ?>
                        </h2>

                        <?php if ($totalResults > 0): ?>
                        <!-- Export Buttons -->
                        <div class="btn-group" role="group" aria-label="Opțiuni export">
                            <a href="sedinte.php?export=txt&session_results=1&dataSedinta=<?php echo urlencode($_POST['dataSedinta'] ?? ''); ?>&institutie=<?php echo urlencode($_POST['institutie'] ?? ''); ?>"
                               class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-file-alt mr-1"></i>
                                Export TXT
                            </a>
                            <a href="sedinte.php?export=xlsx&session_results=1&dataSedinta=<?php echo urlencode($_POST['dataSedinta'] ?? ''); ?>&institutie=<?php echo urlencode($_POST['institutie'] ?? ''); ?>"
                               class="btn btn-outline-success btn-sm">
                                <i class="fas fa-file-excel mr-1"></i>
                                Export Excel
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-body">
                        <?php if ($totalResults === 0): ?>
                        <!-- No Results -->
                        <div class="text-center py-5">
                            <i class="fas fa-calendar-times text-muted" style="font-size: 4rem;"></i>
                            <h3 class="mt-3 text-muted">Nu au fost găsite ședințe</h3>
                            <p class="text-muted">
                                Nu există ședințe programate pentru criteriile de căutare specificate.
                            </p>
                        </div>
                        <?php else: ?>
                        <!-- Results List -->
                        <div class="session-results">
                            <?php foreach ($results as $index => $sedinta): ?>
                            <div class="session-card">
                                <div class="session-header">
                                    <h3 class="session-title">
                                        Ședința <?php echo $index + 1; ?>
                                    </h3>
                                </div>

                                <div class="session-info">
                                    <div class="session-info-item">
                                        <span class="session-info-label">
                                            <i class="fas fa-building mr-1"></i>
                                            Departament:
                                        </span>
                                        <span class="session-info-value">
                                            <?php echo htmlspecialchars($sedinta->departament ?? 'N/A'); ?>
                                        </span>
                                    </div>

                                    <div class="session-info-item">
                                        <span class="session-info-label">
                                            <i class="fas fa-users mr-1"></i>
                                            Complet:
                                        </span>
                                        <span class="session-info-value">
                                            <?php echo htmlspecialchars($sedinta->complet ?? 'N/A'); ?>
                                        </span>
                                    </div>

                                    <div class="session-info-item">
                                        <span class="session-info-label">
                                            <i class="fas fa-calendar mr-1"></i>
                                            Data:
                                        </span>
                                        <span class="session-info-value">
                                            <?php echo htmlspecialchars($sedinta->data ?? 'N/A'); ?>
                                        </span>
                                    </div>

                                    <div class="session-info-item">
                                        <span class="session-info-label">
                                            <i class="fas fa-clock mr-1"></i>
                                            Ora:
                                        </span>
                                        <span class="session-info-value">
                                            <?php echo htmlspecialchars($sedinta->ora ?? 'N/A'); ?>
                                        </span>
                                    </div>
                                </div>

                                <?php if (!empty($sedinta->dosare)): ?>
                                <div class="session-cases">
                                    <h4 class="session-cases-title">
                                        <i class="fas fa-folder mr-1"></i>
                                        Dosare Programate
                                    </h4>
                                    <ul class="session-case-list">
                                        <?php foreach ($sedinta->dosare as $dosar): ?>
                                            <?php if ($dosar && !empty($dosar->numar)): ?>
                                            <li class="session-case-item">
                                                <a href="detalii_dosar.php?numar=<?php echo urlencode($dosar->numar); ?>&institutie=<?php echo urlencode($dosar->institutie ?? ''); ?>"
                                                   class="session-case-link">
                                                    <?php echo htmlspecialchars($dosar->numar); ?>
                                                </a>
                                            </li>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                                <?php else: ?>
                                <div class="session-cases">
                                    <p class="text-muted mb-0">
                                        <i class="fas fa-info-circle mr-1"></i>
                                        Nu sunt dosare programate pentru această ședință.
                                    </p>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Information Section -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h2 class="h5 mb-0">
                            <i class="fas fa-info-circle mr-2"></i>
                            Informații despre Portal
                        </h2>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h3 class="h6 text-primary">Despre Căutarea Ședințelor</h3>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success mr-2"></i>Căutare după data ședinței</li>
                                    <li><i class="fas fa-check text-success mr-2"></i>Filtrare după instituție</li>
                                    <li><i class="fas fa-check text-success mr-2"></i>Informații complete despre ședințe</li>
                                    <li><i class="fas fa-check text-success mr-2"></i>Export în format TXT și Excel</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h3 class="h6 text-primary">Notă Importantă</h3>
                                <p class="text-muted small mb-0">
                                    Datele afișate sunt preluate în timp real de la Portalul Instanțelor de Judecată
                                    și pot fi supuse unor întârzieri sau modificări. Pentru informații oficiale,
                                    consultați <a href="http://portal.just.ro" target="_blank" rel="noopener">portal.just.ro</a>.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Inițializăm loading overlay-ul pentru căutare ședințe
    initSessionLoadingOverlay();

    // Inițializăm navigația mobilă
    initMobileNavigation();

    // Inițializăm validarea formularului
    initFormValidation();

    // Inițializăm calendar picker pentru data ședinței
    initDatePicker();

    // Afișăm conținutul principal după încărcare
    setTimeout(function() {
        const mainContent = document.getElementById('sessionMainContent');
        if (mainContent) {
            mainContent.classList.add('loaded');
        }
    }, 100);
});

/**
 * Inițializează loading overlay-ul pentru căutare ședințe
 */
function initSessionLoadingOverlay() {
    const form = document.getElementById('sessionSearchForm');
    const overlay = document.getElementById('sessionLoadingOverlay');

    if (form && overlay) {
        form.addEventListener('submit', function() {
            overlay.style.display = 'flex';

            // Ascundem overlay-ul după maximum 30 secunde
            setTimeout(function() {
                overlay.classList.add('fade-out');
                setTimeout(function() {
                    overlay.style.display = 'none';
                    overlay.classList.remove('fade-out');
                }, 500);
            }, 30000);
        });
    }
}

/**
 * Inițializează navigația mobilă
 */
function initMobileNavigation() {
    const mobileToggle = document.getElementById('mobileMenuToggle');
    const mainNav = document.getElementById('mainNavigation');

    if (mobileToggle && mainNav) {
        mobileToggle.addEventListener('click', function() {
            const isOpen = mainNav.classList.contains('mobile-open');

            if (isOpen) {
                mainNav.classList.remove('mobile-open');
                mobileToggle.setAttribute('aria-expanded', 'false');
                mobileToggle.innerHTML = '<i class="fas fa-bars" aria-hidden="true"></i>';
            } else {
                mainNav.classList.add('mobile-open');
                mobileToggle.setAttribute('aria-expanded', 'true');
                mobileToggle.innerHTML = '<i class="fas fa-times" aria-hidden="true"></i>';
            }
        });

        // Închide meniul mobil când se face click pe un link
        const navLinks = mainNav.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                mainNav.classList.remove('mobile-open');
                mobileToggle.setAttribute('aria-expanded', 'false');
                mobileToggle.innerHTML = '<i class="fas fa-bars" aria-hidden="true"></i>';
            });
        });
    }
}

/**
 * Inițializează validarea formularului
 */
function initFormValidation() {
    const form = document.getElementById('sessionSearchForm');
    const dateInput = document.getElementById('dataSedinta');

    if (form && dateInput) {
        // Validare în timp real pentru data ședinței
        dateInput.addEventListener('input', function() {
            validateDateInput(this);
        });

        // Validare la submit
        form.addEventListener('submit', function(e) {
            if (!validateForm()) {
                e.preventDefault();
                showNotification('Vă rugăm să corectați erorile din formular.', 'danger');
            }
        });
    }
}

/**
 * Validează input-ul pentru dată
 */
function validateDateInput(input) {
    const value = input.value.trim();
    const datePattern = /^(\d{1,2})\.(\d{1,2})\.(\d{4})$/;

    // Eliminăm clasele de validare anterioare
    input.classList.remove('is-valid', 'is-invalid');

    if (value === '') {
        return; // Câmpul este obligatoriu, dar nu validăm dacă este gol
    }

    if (!datePattern.test(value)) {
        input.classList.add('is-invalid');
        return false;
    }

    const matches = value.match(datePattern);
    const day = parseInt(matches[1]);
    const month = parseInt(matches[2]);
    const year = parseInt(matches[3]);

    // Verificăm validitatea datei
    const date = new Date(year, month - 1, day);
    if (date.getFullYear() !== year || date.getMonth() !== month - 1 || date.getDate() !== day) {
        input.classList.add('is-invalid');
        return false;
    }

    // Verificăm limitele anului
    const currentYear = new Date().getFullYear();
    if (year < 1990 || year > currentYear + 5) {
        input.classList.add('is-invalid');
        return false;
    }

    input.classList.add('is-valid');
    return true;
}

/**
 * Validează întregul formular
 */
function validateForm() {
    const dateInput = document.getElementById('dataSedinta');
    let isValid = true;

    if (dateInput) {
        if (!validateDateInput(dateInput)) {
            isValid = false;
        }

        if (dateInput.value.trim() === '') {
            dateInput.classList.add('is-invalid');
            isValid = false;
        }
    }

    return isValid;
}

/**
 * Inițializează date picker pentru data ședinței
 */
function initDatePicker() {
    const dateInput = document.getElementById('dataSedinta');

    if (dateInput) {
        // Adăugăm un placeholder mai detaliat
        dateInput.setAttribute('placeholder', 'ZZ.LL.AAAA (ex: ' + formatDateToRomanian(new Date()) + ')');

        // Adăugăm un buton pentru data de azi
        const inputGroup = document.createElement('div');
        inputGroup.className = 'input-group';

        const todayButton = document.createElement('button');
        todayButton.type = 'button';
        todayButton.className = 'btn btn-outline-secondary';
        todayButton.innerHTML = '<i class="fas fa-calendar-day mr-1"></i>Azi';
        todayButton.title = 'Setează data de azi';

        todayButton.addEventListener('click', function() {
            dateInput.value = formatDateToRomanian(new Date());
            validateDateInput(dateInput);
        });

        const appendDiv = document.createElement('div');
        appendDiv.className = 'input-group-append';
        appendDiv.appendChild(todayButton);

        // Înlocuim input-ul cu grupul
        const parent = dateInput.parentNode;
        parent.insertBefore(inputGroup, dateInput);
        inputGroup.appendChild(dateInput);
        inputGroup.appendChild(appendDiv);
    }
}

/**
 * Formatează o dată în format românesc DD.MM.YYYY
 */
function formatDateToRomanian(date) {
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}.${month}.${year}`;
}

/**
 * Resetează formularul
 */
function resetForm() {
    const form = document.getElementById('sessionSearchForm');
    if (form) {
        form.reset();

        // Eliminăm clasele de validare
        const inputs = form.querySelectorAll('.form-control');
        inputs.forEach(input => {
            input.classList.remove('is-valid', 'is-invalid');
        });
    }
}

/**
 * Afișează notificări
 */
function showNotification(message, type = 'info') {
    // Implementare simplă de notificare
    const alertClass = type === 'danger' ? 'alert-danger' :
                      type === 'success' ? 'alert-success' :
                      type === 'warning' ? 'alert-warning' : 'alert-info';

    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    `;

    document.body.appendChild(notification);

    // Auto-remove după 5 secunde
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}
</script>

<?php require_once 'includes/footer.php'; ?>
