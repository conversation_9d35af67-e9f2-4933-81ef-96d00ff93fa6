<?php
/**
 * Pagina de căutare a ședințelor de judecată
 * Portal Judiciar - Căutare Ședințe
 */

// CRITICAL FIX: Verificăm IMEDIAT dacă este o cerere de export
// TREBUIE să fie PRIMUL lucru pentru a evita contaminarea cu HTML
if (isset($_GET['export']) && isset($_GET['session_results']) && $_GET['session_results'] === '1') {
    // Includere DOAR fișierele necesare pentru export (fără header HTML)
    require_once 'includes/config.php';
    require_once 'includes/functions.php';
    require_once 'services/SedinteService.php';

    // Procesăm exportul în funcție de tip și ieșim IMEDIAT
    if ($_GET['export'] === 'xlsx') {
        handleSessionExcelExportOnly();
    } else {
        handleSessionCsvExportOnly();
    }
    exit; // STOP - nu mai executăm nimic altceva
}

require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/header.php';
require_once 'services/SedinteService.php';

// Inițializare variabile
$results = [];
$error = null;
$searchParams = [];
$totalResults = 0;
$hasSearchCriteria = false;

// Obținem lista instanțelor pentru dropdown
$institutii = getInstanteList();

// Procesare parametri de intrare
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $dataSedinta = isset($_POST['dataSedinta']) ? trim($_POST['dataSedinta']) : '';
    $institutie = isset($_POST['institutie']) && $_POST['institutie'] !== '' ? $_POST['institutie'] : null;

    // Verificare dacă s-a trimis cel puțin un criteriu de căutare
    $hasSearchCriteria = !empty($dataSedinta) || !empty($institutie);

    if ($hasSearchCriteria) {
        // Validare dată
        $dateValidation = validateRomanianDate($dataSedinta);
        if (!$dateValidation['valid']) {
            $error = $dateValidation['error'];
        } else {
            try {
                // Inițializăm serviciul de ședințe
                $sedinteService = new SedinteService();

                // Construim parametrii de căutare
                $searchParams = [
                    'dataSedinta' => $dateValidation['date'] . 'T00:00:00',
                    'institutie' => $institutie
                ];

                // Efectuăm căutarea
                $results = $sedinteService->cautareSedinte($searchParams);
                $totalResults = count($results);

            } catch (Exception $e) {
                $error = 'Eroare la căutarea ședințelor: ' . $e->getMessage();
                error_log('Eroare căutare ședințe: ' . $e->getMessage());
            }
        }
    } else {
        $error = 'Vă rugăm să introduceți cel puțin un criteriu de căutare (data ședinței sau instituția).';
    }
}

/**
 * Validează data în format românesc DD.MM.YYYY
 */
function validateRomanianDate($dateString) {
    if (empty($dateString)) {
        return ['valid' => false, 'date' => '', 'error' => 'Data ședinței este obligatorie.'];
    }

    // Verificăm formatul DD.MM.YYYY
    if (!preg_match('/^(\d{1,2})\.(\d{1,2})\.(\d{4})$/', $dateString, $matches)) {
        return ['valid' => false, 'date' => '', 'error' => 'Formatul datei trebuie să fie ZZ.LL.AAAA (ex: 15.03.2023)'];
    }

    $day = (int)$matches[1];
    $month = (int)$matches[2];
    $year = (int)$matches[3];

    // Verificăm validitatea datei
    if (!checkdate($month, $day, $year)) {
        return ['valid' => false, 'date' => '', 'error' => 'Data introdusă nu este validă'];
    }

    // Verificăm limitele anului (nu acceptăm date prea vechi sau prea în viitor)
    $currentYear = (int)date('Y');
    if ($year < 1990 || $year > $currentYear + 5) {
        return ['valid' => false, 'date' => '', 'error' => "Anul trebuie să fie între 1990 și " . ($currentYear + 5)];
    }

    // Convertim la format YYYY-MM-DD pentru SOAP API
    $formattedDate = sprintf('%04d-%02d-%02d', $year, $month, $day);

    return ['valid' => true, 'date' => $formattedDate, 'error' => ''];
}

/**
 * DEDICATED CSV EXPORT FUNCTION - NO HTML CONTAMINATION
 */
function handleSessionCsvExportOnly() {
    try {
        // Setăm encoding-ul pentru PHP
        if (function_exists('mb_internal_encoding')) {
            mb_internal_encoding('UTF-8');
        }

        // Procesăm parametrii de căutare pentru API
        $dataSedinta = $_GET['dataSedinta'] ?? '';
        $institutie = $_GET['institutie'] ?? '';

        // Validare dată
        $dateValidation = validateRomanianDate($dataSedinta);
        if (!$dateValidation['valid']) {
            http_response_code(400);
            header('Content-Type: application/json; charset=UTF-8');
            echo json_encode(['error' => $dateValidation['error']], JSON_UNESCAPED_UNICODE);
            return;
        }

        // Inițializăm serviciul de ședințe
        $sedinteService = new SedinteService();

        // Construim parametrii de căutare
        $searchParams = [
            'dataSedinta' => $dateValidation['date'] . 'T00:00:00',
            'institutie' => $institutie ?: null
        ];

        // Obținem rezultatele pentru export
        $allResults = $sedinteService->cautareSedinte($searchParams);

        // Validăm că avem rezultate
        if (empty($allResults)) {
            http_response_code(404);
            header('Content-Type: application/json; charset=UTF-8');
            echo json_encode(['error' => 'Nu există rezultate pentru export'], JSON_UNESCAPED_UNICODE);
            return;
        }

        // Generăm numele fișierului
        $filename = 'Sedinte_' . str_replace('.', '-', $dataSedinta) . '_' . date('Y-m-d_H-i-s') . '.txt';

        // CRITICAL: Curățăm TOATE buffer-ele de output
        while (ob_get_level()) {
            ob_end_clean();
        }

        // Setăm header-ele pentru TXT CURAT
        header('Content-Type: text/plain; charset=UTF-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"; filename*=UTF-8\'\'' . rawurlencode($filename));
        header('Content-Transfer-Encoding: 8bit');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');
        header('X-Content-Type-Options: nosniff');
        header('Content-Description: File Transfer');

        // Generăm DOAR conținutul TXT
        generateSessionTxtContent($allResults);

    } catch (Exception $e) {
        // În caz de eroare, returnăm JSON curat
        http_response_code(500);
        header('Content-Type: application/json; charset=UTF-8');
        echo json_encode(['error' => 'Eroare la generarea CSV: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * DEDICATED EXCEL EXPORT FUNCTION - TRUE XLSX FORMAT
 */
function handleSessionExcelExportOnly() {
    try {
        // Setăm encoding-ul pentru PHP
        if (function_exists('mb_internal_encoding')) {
            mb_internal_encoding('UTF-8');
        }

        // Procesăm parametrii de căutare pentru API
        $dataSedinta = $_GET['dataSedinta'] ?? '';
        $institutie = $_GET['institutie'] ?? '';

        // Validare dată
        $dateValidation = validateRomanianDate($dataSedinta);
        if (!$dateValidation['valid']) {
            http_response_code(400);
            header('Content-Type: application/json; charset=UTF-8');
            echo json_encode(['error' => $dateValidation['error']], JSON_UNESCAPED_UNICODE);
            return;
        }

        // Inițializăm serviciul de ședințe
        $sedinteService = new SedinteService();

        // Construim parametrii de căutare
        $searchParams = [
            'dataSedinta' => $dateValidation['date'] . 'T00:00:00',
            'institutie' => $institutie ?: null
        ];

        // Obținem rezultatele pentru export
        $allResults = $sedinteService->cautareSedinte($searchParams);

        // Validăm că avem rezultate
        if (empty($allResults)) {
            http_response_code(404);
            header('Content-Type: application/json; charset=UTF-8');
            echo json_encode(['error' => 'Nu există rezultate pentru export'], JSON_UNESCAPED_UNICODE);
            return;
        }

        // Generăm numele fișierului în format Excel
        $filename = 'Sedinte_' . str_replace('.', '-', $dataSedinta) . '_' . date('Y-m-d_H-i-s') . '.xlsx';

        // CRITICAL: Curățăm TOATE buffer-ele de output
        while (ob_get_level()) {
            ob_end_clean();
        }

        // Generăm fișierul Excel
        generateSessionExcelFile($allResults, $filename);

    } catch (Exception $e) {
        // În caz de eroare, returnăm JSON curat
        http_response_code(500);
        header('Content-Type: application/json; charset=UTF-8');
        echo json_encode(['error' => 'Eroare la generarea Excel: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * Generează conținutul TXT pentru export ședințe
 */
function generateSessionTxtContent($results) {
    // Header pentru fișierul TXT
    echo "ȘEDINȚE DE JUDECATĂ - REZULTATE CĂUTARE\n";
    echo "Generat la: " . date('d.m.Y H:i:s') . "\n";
    echo "Total ședințe găsite: " . count($results) . "\n";
    echo str_repeat("=", 80) . "\n\n";

    foreach ($results as $index => $sedinta) {
        echo "ȘEDINȚA " . ($index + 1) . "\n";
        echo str_repeat("-", 40) . "\n";
        echo "Departament: " . ($sedinta->departament ?? 'N/A') . "\n";
        echo "Complet: " . ($sedinta->complet ?? 'N/A') . "\n";
        echo "Data: " . ($sedinta->data ?? 'N/A') . "\n";
        echo "Ora: " . ($sedinta->ora ?? 'N/A') . "\n";
        
        if (!empty($sedinta->dosare)) {
            echo "Dosare programate:\n";
            foreach ($sedinta->dosare as $dosar) {
                if ($dosar && !empty($dosar->numar)) {
                    echo "  - " . $dosar->numar . "\n";
                }
            }
        } else {
            echo "Nu sunt dosare programate\n";
        }
        
        echo "\n";
    }
}

/**
 * Generează fișierul Excel pentru export ședințe
 */
function generateSessionExcelFile($results, $filename) {
    // Implementare simplificată pentru Excel - poate fi extinsă cu PhpSpreadsheet
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    
    // Pentru moment, generăm CSV cu extensie xlsx
    echo "Departament,Complet,Data,Ora,Dosare\n";
    
    foreach ($results as $sedinta) {
        $dosareList = '';
        if (!empty($sedinta->dosare)) {
            $dosareNumbers = [];
            foreach ($sedinta->dosare as $dosar) {
                if ($dosar && !empty($dosar->numar)) {
                    $dosareNumbers[] = $dosar->numar;
                }
            }
            $dosareList = implode('; ', $dosareNumbers);
        }
        
        echo '"' . ($sedinta->departament ?? '') . '",';
        echo '"' . ($sedinta->complet ?? '') . '",';
        echo '"' . ($sedinta->data ?? '') . '",';
        echo '"' . ($sedinta->ora ?? '') . '",';
        echo '"' . $dosareList . '"' . "\n";
    }
}
?>

<!-- Stiluri pentru loading overlay pentru căutare ședințe -->
<style>
/* Loading overlay pentru căutare ședințe */
.session-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(44, 62, 80, 0.9);
    z-index: 9998;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 1;
    transition: opacity 0.5s ease-out;
}

.session-loading-overlay.fade-out {
    opacity: 0;
    pointer-events: none;
}

.session-loading-content {
    background-color: #ffffff;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-width: 350px;
    width: 90%;
    border-top: 4px solid #007bff;
}

.session-loading-spinner {
    width: 50px;
    height: 50px;
    margin: 0 auto 1rem;
    border: 4px solid #e9ecef;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: sessionLoadingSpin 1s linear infinite;
}

.session-loading-message {
    color: #2c3e50;
    font-size: 1rem;
    font-weight: 500;
    margin: 0;
    line-height: 1.4;
}

.session-loading-submessage {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    margin-bottom: 0;
}

@keyframes sessionLoadingSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design pentru mobile */
@media (max-width: 767.98px) {
    .session-loading-content {
        padding: 1.5rem;
        max-width: 300px;
    }

    .session-loading-spinner {
        width: 40px;
        height: 40px;
    }

    .session-loading-message {
        font-size: 0.9rem;
    }

    .session-loading-submessage {
        font-size: 0.8rem;
    }
}

/* Asigură că conținutul principal este ascuns inițial */
.session-main-content {
    opacity: 0;
    transition: opacity 0.5s ease-in;
}

.session-main-content.loaded {
    opacity: 1;
}

/* Navigation Bar Styles */
.navbar {
    background-color: #f8f9fa !important;
    border-bottom: 2px solid #007bff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 0.75rem 0;
}

.navbar-brand {
    font-weight: 600;
    color: #2c3e50 !important;
    font-size: 1.25rem;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    color: #007bff !important;
    transform: translateY(-1px);
}

.navbar-brand i {
    color: #007bff;
    margin-right: 0.5rem;
}

.navbar-nav .nav-link {
    color: #2c3e50 !important;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    margin: 0 0.25rem;
}

.navbar-nav .nav-link:hover {
    color: #007bff !important;
    background-color: rgba(0, 123, 255, 0.1);
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    color: #ffffff !important;
    background-color: #007bff;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.navbar-nav .nav-link i {
    margin-right: 0.25rem;
}

/* Responsive navigation */
@media (max-width: 991.98px) {
    .navbar-nav {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #dee2e6;
    }

    .navbar-nav .nav-link {
        margin: 0.25rem 0;
    }
}

@media (max-width: 575.98px) {
    .navbar-brand {
        font-size: 1.1rem;
    }

    .navbar-nav .nav-link {
        font-size: 0.9rem;
        padding: 0.75rem 1rem;
    }
}

/* Notification Container */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
}

.notification-container .alert {
    margin-bottom: 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: none;
    border-radius: 8px;
}

/* Modern Card Styles */
.streamlined-card {
    border: 1px solid #e9ecef;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    background: #ffffff;
}

.streamlined-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.streamlined-card .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 12px 12px 0 0;
    padding: 1.25rem 1.5rem;
}

.streamlined-card .card-body {
    padding: 2rem 2.5rem;
}

.compact-form {
    padding: 1rem 0;
}

.compact-form .form-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

.compact-form .form-control {
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 1rem 1.25rem;
    transition: all 0.3s ease;
    font-size: 1rem;
    min-height: 48px;
}

.compact-form .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.compact-form .mb-3 {
    margin-bottom: 2rem !important;
}

.compact-form .form-text {
    margin-top: 0.75rem;
    font-size: 0.9rem;
    color: #6c757d;
}

/* Searchable Select Styles */
.searchable-select-container {
    position: relative;
    margin-bottom: 1rem;
}

.searchable-select-input {
    border-radius: 8px;
    border: 2px solid #ced4da;
    padding: 1rem 1.25rem;
    transition: all 0.3s ease;
    background: #ffffff;
    font-size: 1rem;
    min-height: 48px;
    width: 100%;
    position: relative;
    z-index: 1;
}

.searchable-select-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: none;
}

.searchable-select-input::placeholder {
    color: #6c757d;
    font-style: italic;
}

.searchable-select-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #ffffff;
    border: 2px solid #007bff;
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 250px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    margin-top: -2px;
}

.searchable-select-dropdown.show {
    display: block;
}

.dropdown-item {
    padding: 1rem 1.25rem;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
    transition: all 0.2s ease;
    font-size: 0.95rem;
    line-height: 1.4;
}

.dropdown-item:hover,
.dropdown-item.highlighted {
    background-color: #f8f9fa;
    color: #2c3e50;
}

.dropdown-item.selected {
    background-color: #007bff;
    color: #ffffff;
    font-weight: 500;
}

.dropdown-item:last-child {
    border-bottom: none;
    border-radius: 0 0 6px 6px;
}

/* Enhanced visibility for institution field */
.institution-field-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 12px;
    padding: 1.5rem;
    margin: 1rem 0;
    border: 1px solid #e9ecef;
}

.institution-field-container .form-label {
    color: #2c3e50;
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.institution-field-container .form-text {
    margin-top: 1rem;
    font-size: 0.9rem;
    color: #6c757d;
    font-style: italic;
}

/* Modern Session Results */
.sessions-grid {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

.session-item {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.session-item:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    transform: translateY(-4px);
    border-color: #007bff;
}

.session-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.25rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f8f9fa;
}

.session-number {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.session-number i {
    font-size: 1.25rem;
}

.session-index {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

.session-time {
    text-align: right;
}

.session-date {
    display: block;
    font-weight: 600;
    color: #2c3e50;
    font-size: 1rem;
}

.session-hour {
    display: block;
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 0.25rem;
}

.session-details {
    margin-bottom: 1.25rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8f9fa;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 500;
    color: #6c757d;
    font-size: 0.9rem;
}

.detail-value {
    font-weight: 600;
    color: #2c3e50;
    text-align: right;
    max-width: 60%;
    word-break: break-word;
}

.session-cases-modern {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    padding: 1rem;
}

.cases-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: #2c3e50;
}

.cases-count {
    font-size: 0.9rem;
}

.cases-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.case-link {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1rem;
    background: #ffffff;
    border: 2px solid #007bff;
    border-radius: 8px;
    color: #007bff;
    text-decoration: none;
    font-size: 0.95rem;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-bottom: 0.5rem;
    position: relative;
    overflow: hidden;
}

.case-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.case-link:hover::before {
    left: 100%;
}

.case-link:hover {
    background: #007bff;
    color: #ffffff;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.case-link:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 123, 255, 0.3);
}

.case-link i {
    margin-right: 0.5rem;
    font-size: 1rem;
}

.cases-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    font-style: italic;
}

/* Empty State */
.empty-state {
    max-width: 400px;
    margin: 0 auto;
}

.empty-state i {
    font-size: 4rem;
    display: block;
    margin-bottom: 1rem;
}

/* ===== RESPONSIVE DESIGN FOR MOBILE ===== */

/* Mobile-specific styles for session search */
@media (max-width: 767px) {
    /* Container adjustments */
    .container-fluid {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    /* Card adjustments for mobile */
    .streamlined-card .card-body {
        padding: 1.5rem 1rem;
    }

    .card-header {
        padding: 1rem;
    }

    /* Form adjustments */
    .compact-form .mb-3 {
        margin-bottom: 1.5rem !important;
    }

    .compact-form .form-control {
        font-size: 16px; /* Prevents zoom on iOS */
        min-height: 48px; /* Touch-friendly size */
        padding: 1rem;
    }

    .searchable-select-input {
        font-size: 16px; /* Prevents zoom on iOS */
        min-height: 48px;
        padding: 1rem;
    }

    .institution-field-container {
        padding: 1rem;
        margin: 0.5rem 0;
    }

    .btn {
        min-height: 48px;
        font-size: 1rem;
        padding: 0.875rem 1.5rem;
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .d-flex.gap-2 {
        flex-direction: column;
        gap: 0.5rem !important;
    }

    /* Session card mobile layout */
    .session-card {
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: 6px;
    }

    .session-header {
        padding-bottom: 0.75rem;
        margin-bottom: 0.75rem;
    }

    .session-title {
        font-size: 1.1rem;
    }

    .session-info {
        display: block; /* Stack items vertically on mobile */
        gap: 0;
    }

    .session-info-item {
        display: flex;
        flex-direction: column;
        margin-bottom: 0.75rem;
        padding: 0.5rem;
        background-color: #f8f9fa;
        border-radius: 4px;
    }

    .session-info-item:last-child {
        margin-bottom: 0;
    }

    .session-info-label {
        font-size: 0.85rem;
        color: #6c757d;
        margin-bottom: 0.25rem;
        min-width: auto;
    }

    .session-info-value {
        font-size: 0.95rem;
        font-weight: 500;
        color: #2c3e50;
    }

    /* Session cases mobile layout */
    .session-cases {
        padding: 0.75rem;
        margin-top: 0.75rem;
    }

    .session-cases-title {
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .session-case-item {
        padding: 0.5rem 0;
    }

    .session-case-link {
        font-size: 0.9rem;
        word-break: break-all; /* Ensure long case numbers break properly */
    }

    /* Mobile case links */
    .case-link {
        width: 100%;
        justify-content: flex-start;
        padding: 1rem;
        font-size: 1rem;
        margin-bottom: 0.75rem;
        min-height: 48px;
    }

    .case-link i {
        margin-right: 0.75rem;
        font-size: 1.1rem;
    }

    /* Export buttons mobile layout */
    .btn-group {
        display: flex;
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        border-radius: 6px !important;
        margin-bottom: 0.5rem;
        width: 100%;
    }

    .btn-group .btn:last-child {
        margin-bottom: 0;
    }

    /* Search results header mobile */
    .card-header .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
    }

    .card-header .h5 {
        margin-bottom: 1rem;
    }

    /* Input group mobile adjustments */
    .input-group {
        flex-wrap: wrap;
    }

    .input-group .form-control {
        flex: 1 1 100%;
        margin-bottom: 0.5rem;
    }

    .input-group-append {
        flex: 1 1 100%;
    }

    .input-group-append .btn {
        width: 100%;
        border-radius: 6px;
    }

    /* Alert adjustments for mobile */
    .alert {
        margin-bottom: 1rem;
        padding: 0.75rem;
        font-size: 0.9rem;
    }

    /* Information section mobile */
    .card-body .row {
        margin: 0;
    }

    .card-body .col-md-6 {
        padding: 0;
        margin-bottom: 1rem;
    }

    .card-body .col-md-6:last-child {
        margin-bottom: 0;
    }

    /* No results section mobile */
    .text-center.py-5 {
        padding: 2rem 1rem !important;
    }

    .text-center.py-5 i {
        font-size: 3rem !important;
    }

    .text-center.py-5 h3 {
        font-size: 1.25rem;
        margin-top: 1rem !important;
    }
}

/* Tablet-specific adjustments */
@media (min-width: 768px) and (max-width: 991px) {
    .session-info {
        grid-template-columns: repeat(2, 1fr);
    }

    .session-info-item {
        flex-direction: row;
        align-items: center;
    }

    .session-info-label {
        min-width: 100px;
    }
}

/* Large screen optimizations */
@media (min-width: 1200px) {
    .container-fluid {
        max-width: 1400px;
        margin: 0 auto;
    }

    .session-info {
        grid-template-columns: repeat(4, 1fr);
    }

    .session-card {
        padding: 2rem;
    }
}

/* Print styles */
@media print {
    .judicial-navbar,
    .btn,
    .btn-group,
    .alert,
    .card-header,
    #sessionLoadingOverlay {
        display: none !important;
    }

    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
        page-break-inside: avoid;
    }

    .session-card {
        border: 1px solid #000 !important;
        margin-bottom: 1rem !important;
        page-break-inside: avoid;
    }

    .session-title {
        color: #000 !important;
    }

    .session-info-label,
    .session-info-value {
        color: #000 !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .session-card {
        border: 2px solid #000;
    }

    .session-header {
        border-bottom: 3px solid #000;
    }

    .session-info-item {
        border: 1px solid #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .session-card,
    .nav-link,
    .btn,
    .session-loading-overlay {
        transition: none !important;
        animation: none !important;
    }
}
</style>

<!-- Loading overlay pentru căutare ședințe -->
<div id="sessionLoadingOverlay" class="session-loading-overlay" style="display: none;">
    <div class="session-loading-content">
        <div class="session-loading-spinner"></div>
        <p class="session-loading-message">Se caută ședințele...</p>
        <p class="session-loading-submessage">Vă rugăm să așteptați</p>
    </div>
</div>

<!-- Notification Container -->
<div id="notificationContainer" class="notification-container" style="display: none;">
    <div id="notification" class="alert" role="alert"></div>
</div>

<!-- Navigation Bar -->
<nav class="navbar navbar-expand-lg navbar-light bg-light border-bottom">
    <div class="container">
        <a class="navbar-brand" href="index.php">
            <i class="fas fa-gavel me-2"></i>
            DosareJust.ro - Portal Judiciar
        </a>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav ms-auto">
                <li class="nav-item">
                    <a class="nav-link" href="index.php">
                        <i class="fas fa-search me-1"></i>
                        Căutare Dosare
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="sedinte.php">
                        <i class="fas fa-calendar-alt me-1"></i>
                        Ședințe
                    </a>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- Main Content -->
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-12 col-xl-10">
            <!-- Main Search Form -->
            <div class="card streamlined-card compact-form">
                <div class="card-header">
                    <h1 class="h4 mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>
                        Căutare Ședințe de Judecată
                    </h1>
                </div>
                <div class="card-body">
                    <p class="mb-3 text-muted">
                        Căutați ședințele de judecată programate pe o anumită dată și/sau la o anumită instituție.
                    </p>

                    <form method="POST" action="sedinte.php" id="sessionSearchForm">
                        <div class="row">
                            <!-- Data Ședinței -->
                            <div class="col-md-6 mb-3">
                                <label for="dataSedinta" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>
                                    Data Ședinței <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <input type="text"
                                           class="form-control"
                                           id="dataSedinta"
                                           name="dataSedinta"
                                           placeholder="ZZ.LL.AAAA (ex: 15.03.2024)"
                                           value="<?php echo htmlspecialchars($_POST['dataSedinta'] ?? ''); ?>"
                                           required
                                           pattern="^(\d{1,2})\.(\d{1,2})\.(\d{4})$"
                                           title="Formatul datei trebuie să fie ZZ.LL.AAAA">
                                    <div class="input-group-append">
                                        <button type="button" class="btn btn-outline-secondary" id="todayBtn" title="Setează data de azi">
                                            <i class="fas fa-calendar-day"></i>
                                            Azi
                                        </button>
                                    </div>
                                </div>
                                <div class="form-text">
                                    Introduceți data în format românesc (ZZ.LL.AAAA)
                                </div>
                            </div>

                            <!-- Instituția -->
                            <div class="col-md-6 mb-3">
                                <div class="institution-field-container">
                                    <label for="institutie" class="form-label">
                                        <i class="fas fa-university me-2"></i>
                                        Instituția
                                    </label>
                                    <div class="searchable-select-container">
                                        <input type="text"
                                               class="form-control searchable-select-input"
                                               id="institutieSearch"
                                               placeholder="Căutați sau selectați o instituție..."
                                               autocomplete="off">
                                        <select class="form-control d-none" id="institutie" name="institutie">
                                            <option value="">-- Toate instituțiile --</option>
                                            <?php foreach ($institutii as $cod => $nume): ?>
                                                <option value="<?php echo htmlspecialchars($cod); ?>"
                                                        <?php echo (isset($_POST['institutie']) && $_POST['institutie'] === $cod) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($nume); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <div class="searchable-select-dropdown" id="institutieDropdown"></div>
                                    </div>
                                    <div class="form-text">
                                        Opțional: căutați și selectați o instituție specifică
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="row">
                            <div class="col-md-8">
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>
                                        Caută Ședințe
                                    </button>
                                    <button type="reset" class="btn btn-secondary" onclick="resetForm()">
                                        <i class="fas fa-eraser me-2"></i>
                                        Resetează
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

                <?php if ($error): ?>
                <!-- Error Message -->
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    <strong>Eroare:</strong> <?php echo htmlspecialchars($error); ?>
                </div>
                <?php endif; ?>

            <?php if ($hasSearchCriteria && !$error): ?>
            <!-- Search Results -->
            <div class="card streamlined-card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h2 class="h5 mb-0">
                        <i class="fas fa-calendar-check me-2"></i>
                        Ședințe găsite
                        <?php if ($totalResults > 0): ?>
                            <span class="badge bg-primary ms-2"><?php echo $totalResults; ?></span>
                        <?php endif; ?>
                    </h2>

                    <?php if ($totalResults > 0): ?>
                    <!-- Export Buttons -->
                    <div class="btn-group" role="group" aria-label="Opțiuni export">
                        <a href="sedinte.php?export=txt&session_results=1&dataSedinta=<?php echo urlencode($_POST['dataSedinta'] ?? ''); ?>&institutie=<?php echo urlencode($_POST['institutie'] ?? ''); ?>"
                           class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-file-alt me-1"></i>
                            Export TXT
                        </a>
                        <a href="sedinte.php?export=xlsx&session_results=1&dataSedinta=<?php echo urlencode($_POST['dataSedinta'] ?? ''); ?>&institutie=<?php echo urlencode($_POST['institutie'] ?? ''); ?>"
                           class="btn btn-outline-success btn-sm">
                            <i class="fas fa-file-excel me-1"></i>
                            Export Excel
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <?php if ($totalResults === 0): ?>
                    <!-- No Results -->
                    <div class="text-center py-5">
                        <div class="empty-state">
                            <i class="fas fa-calendar-times text-muted mb-3"></i>
                            <h4 class="text-muted mb-2">Nu au fost găsite ședințe</h4>
                            <p class="text-muted mb-0">
                                Nu există ședințe programate pentru criteriile de căutare specificate.
                            </p>
                        </div>
                    </div>
                    <?php else: ?>
                    <!-- Modern Minimalist Results -->
                    <div class="sessions-grid">
                        <?php foreach ($results as $index => $sedinta): ?>
                        <div class="session-item">
                            <div class="session-item-header">
                                <div class="session-number">
                                    <i class="fas fa-calendar-alt text-primary"></i>
                                    <span class="session-index"><?php echo $index + 1; ?></span>
                                </div>
                                <div class="session-time">
                                    <span class="session-date"><?php echo htmlspecialchars($sedinta->data ?? 'N/A'); ?></span>
                                    <span class="session-hour"><?php echo htmlspecialchars($sedinta->ora ?? 'N/A'); ?></span>
                                </div>
                            </div>

                            <div class="session-details">
                                <div class="detail-row">
                                    <span class="detail-label">Departament</span>
                                    <span class="detail-value"><?php echo htmlspecialchars($sedinta->departament ?? 'N/A'); ?></span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">Complet</span>
                                    <span class="detail-value"><?php echo htmlspecialchars($sedinta->complet ?? 'N/A'); ?></span>
                                </div>
                            </div>

                            <?php if (!empty($sedinta->dosare)): ?>
                            <div class="session-cases-modern">
                                <div class="cases-header">
                                    <i class="fas fa-folder-open text-primary me-1"></i>
                                    <span class="cases-count"><?php echo count($sedinta->dosare); ?> dosare programate</span>
                                </div>
                                <div class="cases-list">
                                    <?php foreach ($sedinta->dosare as $dosar): ?>
                                        <?php if ($dosar && !empty($dosar->numar)): ?>
                                        <?php
                                        // Use the institution from the search form if available, otherwise use the case institution
                                        $institutieParam = '';
                                        if (!empty($_POST['institutie'])) {
                                            $institutieParam = $_POST['institutie'];
                                        } elseif (!empty($dosar->institutie)) {
                                            $institutieParam = $dosar->institutie;
                                        }
                                        ?>
                                        <a href="detalii_dosar.php?numar=<?php echo urlencode($dosar->numar); ?>&institutie=<?php echo urlencode($institutieParam); ?>"
                                           class="case-link"
                                           title="Vizualizează detaliile dosarului <?php echo htmlspecialchars($dosar->numar); ?>"
                                           data-case-number="<?php echo htmlspecialchars($dosar->numar); ?>"
                                           data-institution="<?php echo htmlspecialchars($institutieParam); ?>">
                                            <i class="fas fa-folder-open me-1"></i>
                                            <?php echo htmlspecialchars($dosar->numar); ?>
                                        </a>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <?php else: ?>
                            <div class="session-cases-modern">
                                <div class="cases-empty">
                                    <i class="fas fa-info-circle text-muted me-1"></i>
                                    <span class="text-muted">Nu sunt dosare programate</span>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Information Section -->
            <div class="card streamlined-card mt-4">
                <div class="card-header">
                    <h2 class="h5 mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Informații despre Portal
                    </h2>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h3 class="h6 text-primary">Despre Căutarea Ședințelor</h3>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Căutare după data ședinței</li>
                                <li><i class="fas fa-check text-success me-2"></i>Filtrare după instituție</li>
                                <li><i class="fas fa-check text-success me-2"></i>Informații complete despre ședințe</li>
                                <li><i class="fas fa-check text-success me-2"></i>Export în format TXT și Excel</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h3 class="h6 text-primary">Notă Importantă</h3>
                            <p class="text-muted small mb-0">
                                Datele afișate sunt preluate în timp real de la Portalul Instanțelor de Judecată
                                și pot fi supuse unor întârzieri sau modificări. Pentru informații oficiale,
                                consultați <a href="http://portal.just.ro" target="_blank" rel="noopener">portal.just.ro</a>.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Inițializăm loading overlay-ul pentru căutare ședințe
    initSessionLoadingOverlay();

    // Inițializăm navigația mobilă
    initMobileNavigation();

    // Inițializăm validarea formularului
    initFormValidation();

    // Inițializăm calendar picker pentru data ședinței
    initDatePicker();

    // Inițializăm dropdown-ul searchable pentru instituții
    initSearchableInstitutionDropdown();

    // Inițializăm gestionarea link-urilor pentru dosare
    initCaseLinksHandling();

    // Afișăm conținutul principal după încărcare
    setTimeout(function() {
        const mainContent = document.getElementById('sessionMainContent');
        if (mainContent) {
            mainContent.classList.add('loaded');
        }
    }, 100);
});

/**
 * Inițializează loading overlay-ul pentru căutare ședințe
 */
function initSessionLoadingOverlay() {
    const form = document.getElementById('sessionSearchForm');
    const overlay = document.getElementById('sessionLoadingOverlay');

    if (form && overlay) {
        form.addEventListener('submit', function() {
            overlay.style.display = 'flex';

            // Ascundem overlay-ul după maximum 30 secunde
            setTimeout(function() {
                overlay.classList.add('fade-out');
                setTimeout(function() {
                    overlay.style.display = 'none';
                    overlay.classList.remove('fade-out');
                }, 500);
            }, 30000);
        });
    }
}

/**
 * Inițializează navigația mobilă
 */
function initMobileNavigation() {
    const mobileToggle = document.getElementById('mobileMenuToggle');
    const mainNav = document.getElementById('mainNavigation');

    if (mobileToggle && mainNav) {
        mobileToggle.addEventListener('click', function() {
            const isOpen = mainNav.classList.contains('mobile-open');

            if (isOpen) {
                mainNav.classList.remove('mobile-open');
                mobileToggle.setAttribute('aria-expanded', 'false');
                mobileToggle.innerHTML = '<i class="fas fa-bars" aria-hidden="true"></i>';
            } else {
                mainNav.classList.add('mobile-open');
                mobileToggle.setAttribute('aria-expanded', 'true');
                mobileToggle.innerHTML = '<i class="fas fa-times" aria-hidden="true"></i>';
            }
        });

        // Închide meniul mobil când se face click pe un link
        const navLinks = mainNav.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                mainNav.classList.remove('mobile-open');
                mobileToggle.setAttribute('aria-expanded', 'false');
                mobileToggle.innerHTML = '<i class="fas fa-bars" aria-hidden="true"></i>';
            });
        });
    }
}

/**
 * Inițializează validarea formularului
 */
function initFormValidation() {
    const form = document.getElementById('sessionSearchForm');
    const dateInput = document.getElementById('dataSedinta');

    if (form && dateInput) {
        // Validare în timp real pentru data ședinței
        dateInput.addEventListener('input', function() {
            validateDateInput(this);
        });

        // Validare la submit
        form.addEventListener('submit', function(e) {
            if (!validateForm()) {
                e.preventDefault();
                showNotification('Vă rugăm să corectați erorile din formular.', 'danger');
            }
        });
    }
}

/**
 * Validează input-ul pentru dată
 */
function validateDateInput(input) {
    const value = input.value.trim();
    const datePattern = /^(\d{1,2})\.(\d{1,2})\.(\d{4})$/;

    // Eliminăm clasele de validare anterioare
    input.classList.remove('is-valid', 'is-invalid');

    if (value === '') {
        return; // Câmpul este obligatoriu, dar nu validăm dacă este gol
    }

    if (!datePattern.test(value)) {
        input.classList.add('is-invalid');
        return false;
    }

    const matches = value.match(datePattern);
    const day = parseInt(matches[1]);
    const month = parseInt(matches[2]);
    const year = parseInt(matches[3]);

    // Verificăm validitatea datei
    const date = new Date(year, month - 1, day);
    if (date.getFullYear() !== year || date.getMonth() !== month - 1 || date.getDate() !== day) {
        input.classList.add('is-invalid');
        return false;
    }

    // Verificăm limitele anului
    const currentYear = new Date().getFullYear();
    if (year < 1990 || year > currentYear + 5) {
        input.classList.add('is-invalid');
        return false;
    }

    input.classList.add('is-valid');
    return true;
}

/**
 * Validează întregul formular
 */
function validateForm() {
    const dateInput = document.getElementById('dataSedinta');
    let isValid = true;

    if (dateInput) {
        if (!validateDateInput(dateInput)) {
            isValid = false;
        }

        if (dateInput.value.trim() === '') {
            dateInput.classList.add('is-invalid');
            isValid = false;
        }
    }

    return isValid;
}

/**
 * Inițializează date picker pentru data ședinței
 */
function initDatePicker() {
    const todayBtn = document.getElementById('todayBtn');
    const dateInput = document.getElementById('dataSedinta');

    if (todayBtn && dateInput) {
        todayBtn.addEventListener('click', function() {
            dateInput.value = formatDateToRomanian(new Date());
            validateDateInput(dateInput);
        });
    }
}

/**
 * Inițializează dropdown-ul searchable pentru instituții
 */
function initSearchableInstitutionDropdown() {
    const searchInput = document.getElementById('institutieSearch');
    const hiddenSelect = document.getElementById('institutie');
    const dropdown = document.getElementById('institutieDropdown');

    if (!searchInput || !hiddenSelect || !dropdown) {
        return;
    }

    // Construim lista de opțiuni din select-ul ascuns
    const options = Array.from(hiddenSelect.options).map(option => ({
        value: option.value,
        text: option.text,
        selected: option.selected
    }));

    // Setăm valoarea inițială dacă există o selecție
    const selectedOption = options.find(opt => opt.selected);
    if (selectedOption && selectedOption.value !== '') {
        searchInput.value = selectedOption.text;
    }

    let highlightedIndex = -1;

    // Funcție pentru afișarea opțiunilor
    function showOptions(filteredOptions = options) {
        dropdown.innerHTML = '';

        if (filteredOptions.length === 0) {
            const noResults = document.createElement('div');
            noResults.className = 'dropdown-item';
            noResults.textContent = 'Nu au fost găsite rezultate';
            noResults.style.fontStyle = 'italic';
            noResults.style.color = '#6c757d';
            dropdown.appendChild(noResults);
        } else {
            filteredOptions.forEach((option, index) => {
                const item = document.createElement('div');
                item.className = 'dropdown-item';
                item.textContent = option.text;
                item.dataset.value = option.value;
                item.dataset.index = index;

                if (option.selected) {
                    item.classList.add('selected');
                }

                item.addEventListener('click', function() {
                    selectOption(option);
                });

                dropdown.appendChild(item);
            });
        }

        dropdown.classList.add('show');
        highlightedIndex = -1;
    }

    // Funcție pentru selectarea unei opțiuni
    function selectOption(option) {
        searchInput.value = option.text;
        hiddenSelect.value = option.value;
        dropdown.classList.remove('show');

        // Actualizăm starea selected în opțiuni
        options.forEach(opt => opt.selected = false);
        option.selected = true;

        // Trigger change event pentru validare
        hiddenSelect.dispatchEvent(new Event('change'));
    }

    // Funcție pentru filtrarea opțiunilor
    function filterOptions(query) {
        const normalizedQuery = normalizeRomanianText(query.toLowerCase());
        return options.filter(option => {
            const normalizedText = normalizeRomanianText(option.text.toLowerCase());
            return normalizedText.includes(normalizedQuery);
        });
    }

    // Funcție pentru normalizarea textului românesc
    function normalizeRomanianText(text) {
        return text
            .replace(/ă/g, 'a')
            .replace(/â/g, 'a')
            .replace(/î/g, 'i')
            .replace(/ș/g, 's')
            .replace(/ț/g, 't');
    }

    // Event listeners pentru input
    searchInput.addEventListener('input', function() {
        const query = this.value.trim();

        if (query === '') {
            showOptions();
        } else {
            const filtered = filterOptions(query);
            showOptions(filtered);
        }
    });

    searchInput.addEventListener('focus', function() {
        const query = this.value.trim();
        if (query === '') {
            showOptions();
        } else {
            const filtered = filterOptions(query);
            showOptions(filtered);
        }
    });

    // Navigare cu tastatura
    searchInput.addEventListener('keydown', function(e) {
        const items = dropdown.querySelectorAll('.dropdown-item:not([style*="italic"])');

        if (e.key === 'ArrowDown') {
            e.preventDefault();
            highlightedIndex = Math.min(highlightedIndex + 1, items.length - 1);
            updateHighlight(items);
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            highlightedIndex = Math.max(highlightedIndex - 1, -1);
            updateHighlight(items);
        } else if (e.key === 'Enter') {
            e.preventDefault();
            if (highlightedIndex >= 0 && items[highlightedIndex]) {
                const value = items[highlightedIndex].dataset.value;
                const option = options.find(opt => opt.value === value);
                if (option) {
                    selectOption(option);
                }
            }
        } else if (e.key === 'Escape') {
            dropdown.classList.remove('show');
            highlightedIndex = -1;
        }
    });

    // Funcție pentru actualizarea highlight-ului
    function updateHighlight(items) {
        items.forEach((item, index) => {
            item.classList.toggle('highlighted', index === highlightedIndex);
        });

        // Scroll la elementul highlighted
        if (highlightedIndex >= 0 && items[highlightedIndex]) {
            items[highlightedIndex].scrollIntoView({
                block: 'nearest',
                behavior: 'smooth'
            });
        }
    }

    // Închide dropdown-ul când se face click în afara lui
    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !dropdown.contains(e.target)) {
            dropdown.classList.remove('show');
        }
    });

    // Reset la schimbarea formularului
    const form = document.getElementById('sessionSearchForm');
    if (form) {
        form.addEventListener('reset', function() {
            setTimeout(() => {
                searchInput.value = '';
                hiddenSelect.value = '';
                options.forEach(opt => opt.selected = opt.value === '');
                dropdown.classList.remove('show');
            }, 10);
        });
    }
}

/**
 * Formatează o dată în format românesc DD.MM.YYYY
 */
function formatDateToRomanian(date) {
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}.${month}.${year}`;
}

/**
 * Resetează formularul
 */
function resetForm() {
    const form = document.getElementById('sessionSearchForm');
    if (form) {
        form.reset();

        // Eliminăm clasele de validare
        const inputs = form.querySelectorAll('.form-control');
        inputs.forEach(input => {
            input.classList.remove('is-valid', 'is-invalid');
        });
    }
}

/**
 * Inițializează gestionarea link-urilor pentru dosare
 */
function initCaseLinksHandling() {
    const caseLinks = document.querySelectorAll('.case-link');

    caseLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Adăugăm un indicator vizual că link-ul a fost accesat
            this.style.opacity = '0.7';

            // Verificăm dacă avem parametrii necesari
            const caseNumber = this.dataset.caseNumber;
            const institution = this.dataset.institution;

            if (!caseNumber) {
                e.preventDefault();
                showNotification('Eroare: Numărul dosarului nu este disponibil.', 'danger');
                this.style.opacity = '1';
                return;
            }

            // Afișăm un mesaj de încărcare
            showNotification(`Se încarcă detaliile dosarului ${caseNumber}...`, 'info');

            // Logăm navigarea pentru debugging
            console.log('Navigare către dosar:', {
                numar: caseNumber,
                institutie: institution,
                url: this.href
            });
        });

        // Restaurăm opacitatea la hover out
        link.addEventListener('mouseleave', function() {
            this.style.opacity = '1';
        });
    });
}

/**
 * Afișează notificări
 */
function showNotification(message, type = 'info') {
    // Implementare simplă de notificare
    const alertClass = type === 'danger' ? 'alert-danger' :
                      type === 'success' ? 'alert-success' :
                      type === 'warning' ? 'alert-warning' : 'alert-info';

    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    `;

    document.body.appendChild(notification);

    // Auto-remove după 5 secunde
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}
</script>

<?php require_once 'includes/footer.php'; ?>
